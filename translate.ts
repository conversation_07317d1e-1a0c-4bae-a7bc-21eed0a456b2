#!/usr/bin/env ts-node
import { translate } from "@vitalets/google-translate-api";
import { cloneDeep, difference, merge } from "es-toolkit";
import stringify from "fast-json-stable-stringify";
import fs from "fs";
import inquirer from "inquirer";
import { exec } from "node:child_process";
import { JsxText, Project, StringLiteral, SyntaxKind } from "ts-morph";
import { errors } from "./src/lib/errors";

const project = new Project({
  tsConfigFilePath: "tsconfig.json",
});

const ignoreTexts: (string | RegExp)[] = ["EN 🇬🇧", "FR 🇫🇷", "Shorts", "(", ")", /^[^a-zA-Z]+$/];

function checkIgnoreText(text: string) {
  return ignoreTexts.some((x) => (typeof x === "string" ? x === text : x.test(text)));
}

function getRelativePath(path: string) {
  if (path.startsWith(import.meta.dirname)) return path.slice(import.meta.dirname.length + 1);
}

await (async () => {
  // Collect all text nodes
  const items: { file: string; text: string; node: JsxText | StringLiteral }[] = [];

  for (const sourceFile of project.getSourceFiles("**/*.tsx")) {
    const jsxTexts = sourceFile.getDescendantsOfKind(SyntaxKind.JsxText);
    // const stringLiterals = sourceFile.getDescendantsOfKind(SyntaxKind.StringLiteral);

    jsxTexts.forEach((n) => {
      const text = n.getText().trim();
      if (text && !checkIgnoreText(text)) items.push({ file: sourceFile.getFilePath(), text, node: n });
    });

    // stringLiterals.forEach((n) => {
    //   // only if it's inside JSX attribute
    //   if (n.getParent()?.getKind() === SyntaxKind.JsxAttribute) {
    //     const text = n.getText().slice(1, -1); // remove quotes
    //     if (text) items.push({ file: sourceFile.getFilePath(), text, node: n });
    //   }
    // });
  }

  if (items.length === 0) {
    console.log("No text nodes found.");
    return;
  }

  console.log(`Found ${items.length} text nodes.`);
  console.log(items.map((item) => `[${getRelativePath(item.file)}] "${item.text}"`).join("\n") + "\n\n");

  // Let user pick with checkboxes
  const choices = items.map((item, i) => ({
    name: `"${item.text}"`,
    value: i,
  }));

  const { selected } = await inquirer.prompt([
    {
      type: "checkbox",
      name: "selected",
      message: "Select texts to wrap with t('...'):",
      choices,
      pageSize: 20,
    },
  ]);

  for (const idx of selected as number[]) {
    const { node, file, text } = items[idx];
    const sourceFile = project.getSourceFileOrThrow(file);

    if (isClientComponent(file, project)) {
      // Ensure const { t } = useTranslation(); in function body
      const func =
        node.getFirstAncestorByKind(SyntaxKind.FunctionDeclaration) ||
        node.getFirstAncestorByKind(SyntaxKind.ArrowFunction) ||
        node.getFirstAncestorByKind(SyntaxKind.FunctionExpression);

      if (func) {
        const block = func.getFirstDescendantByKind(SyntaxKind.Block);
        if (block) {
          const hasT = block
            .getDescendantsOfKind(SyntaxKind.VariableDeclaration)
            .some((decl) => decl.getName() === "{ t }");
          if (!hasT) {
            block.insertStatements(0, "const { t } = useTranslation();");
          }
        }
      }

      // Wrap in t()
      if (node.getKind() === SyntaxKind.JsxText || node.getKind() === SyntaxKind.StringLiteral) {
        node.replaceWithText(`{t("${text}")}`);
      }

      // Ensure import { useTranslation } from 'react-i18next'
      const importDecl = sourceFile.getImportDeclaration((imp) => imp.getModuleSpecifierValue() === "react-i18next");
      if (!importDecl) {
        sourceFile.addImportDeclaration({
          namedImports: ["useTranslation"],
          moduleSpecifier: "react-i18next",
        });
      } else if (!importDecl.getNamedImports().some((i) => i.getName() === "useTranslation")) {
        importDecl.addNamedImport("useTranslation");
      }
    } else {
      // Wrap in T
      if (node.getKind() === SyntaxKind.JsxText || node.getKind() === SyntaxKind.StringLiteral) {
        node.replaceWithText(`<T text="${text}" />`);
      }

      // Ensure import T from '@/components/T.tsx'
      const hasImport = sourceFile.getImportDeclarations().some((imp) => {
        const moduleSpec = imp.getModuleSpecifierValue();
        if (moduleSpec === "@/components/T") {
          const defaultImport = imp.getDefaultImport();
          return defaultImport?.getText() === "T";
        }
        return false;
      });

      if (!hasImport) {
        sourceFile.addImportDeclaration({
          defaultImport: "T",
          moduleSpecifier: "@/components/T",
        });
      }
    }
  }

  await project.save();
  exec("npm exec prettier -- --write .");
  console.log("✅ Transformation complete.");
})();

await (async () => {
  const keys = new Set<string>();

  for (const sourceFile of project.getSourceFiles("**/*.{ts,tsx}")) {
    const calls = sourceFile.getDescendantsOfKind(SyntaxKind.CallExpression);

    calls.forEach((call) => {
      const expr = call.getExpression().getText();
      if (expr === "t") {
        const args = call.getArguments();
        if (args.length > 0 && args[0].getKind() === SyntaxKind.StringLiteral) {
          const text = args[0].getText().slice(1, -1); // remove quotes
          keys.add(text);
        }
      }
    });

    const jsxElements = [
      ...sourceFile.getDescendantsOfKind(SyntaxKind.JsxSelfClosingElement),
      ...sourceFile.getDescendantsOfKind(SyntaxKind.JsxOpeningElement),
    ];

    jsxElements.forEach((jsx) => {
      if (jsx.getTagNameNode().getText() === "T") {
        const textAttr = jsx
          .getAttributes()
          .find(
            (attr) =>
              attr.getKind() === SyntaxKind.JsxAttribute &&
              "getNameNode" in attr &&
              attr.getNameNode().getText() === "text",
          );

        if (textAttr && textAttr.getFirstChildByKind(SyntaxKind.StringLiteral)) {
          const value = textAttr.getFirstChildByKind(SyntaxKind.StringLiteral)!.getLiteralText();
          keys.add(value);
        }
      }
    });
  }

  for (const error of Object.values(errors)) {
    keys.add(error);
  }

  // Build translation object
  const existingTranslations: Record<"en" | "fr", { translation: Record<string, string> }> = JSON.parse(
    fs.readFileSync("src/assets/translations.json", "utf8"),
  );
  const translationMap: Record<string, string> = {};
  keys.forEach((k) => (translationMap[k] = k));
  const translations: Record<"en" | "fr", { translation: Record<string, string> }> = {
    en: { translation: translationMap },
    fr: { translation: cloneDeep(translationMap) },
  };

  const extraKeys = difference(
    Object.keys(existingTranslations.en.translation),
    Object.keys(translations.en.translation),
  );
  if (extraKeys.length) {
    const { selected } = await inquirer.prompt([
      {
        type: "checkbox",
        name: "selected",
        message: "These keys are no longer in use, do you want to delete them?",
        choices: extraKeys.map((k, i) => ({ name: k, value: i })),
        pageSize: 20,
      },
    ]);
    selected.forEach((idx: number) => {
      const k = extraKeys[idx];
      delete existingTranslations.en.translation[k];
      delete existingTranslations.fr.translation[k];
    });
  }

  merge(translations, existingTranslations);

  const newKeys = difference(
    Object.keys(translations.fr.translation),
    Object.keys(existingTranslations.fr.translation),
  );
  if (newKeys.length) {
    console.log("These keys are new, generating fr translations for them:", newKeys);

    const translated = await translateBatch(newKeys, "fr");
    newKeys.forEach((k, i) => (translations.fr.translation[k] = translated[i]));
  }

  // Save as JSON
  fs.writeFileSync("src/assets/translations.json", stringify(translations));
  exec("npm exec prettier -- --write src/assets/translations.json");

  console.log("✅ Extracted translations to translations.json");
})();

async function translateBatch(texts: string[], target = "fr") {
  const joined = texts.join("\n");
  const { text } = await translate(joined, { to: target });
  // Split translated text back into array
  return text.split("\n");
}

function isClientComponent(filePath: string, project: Project): boolean {
  const sourceFile = project.getSourceFileOrThrow(filePath);

  // Get all top-level statements
  const statements = sourceFile.getStatements();

  if (statements.length === 0) return false;

  const first = statements[0];

  // Check if the first statement is "use client"
  if (first.getKind() === SyntaxKind.ExpressionStatement) {
    const expr = first.asKindOrThrow(SyntaxKind.ExpressionStatement).getExpression();

    if (expr.getKind() === SyntaxKind.StringLiteral) {
      const text = expr.asKindOrThrow(SyntaxKind.StringLiteral).getLiteralText();
      return text === "use client";
    }
  }

  return false;
}
