services:
  redis:
    image: redis:latest
    restart: always
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
    networks:
      - shorts-network

  website:
    build:
      context: ./
      dockerfile: Dockerfile-prod
    container_name: shorts-website
    ports:
      - 5002:5002
    networks:
      - shorts-network
    env_file:
      - path: ./.env
        required: true
    restart: always

  db_sync:
    build:
      context: ./
      dockerfile: src/database/drizzle/sync/Dockerfile
    container_name: db-sync
    volumes:
      - ./:/app/
    networks:
      - shorts-network
    env_file:
      - path: ./.env
        required: true

  jobs:
    build:
      context: ./
      dockerfile: src/jobs/Dockerfile-prod
    container_name: shorts-jobs
    networks:
      - shorts-network
    env_file:
      - path: ./.env
        required: true
    restart: always

networks:
  shorts-network:
    driver: bridge
