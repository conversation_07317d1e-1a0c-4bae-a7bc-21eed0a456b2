services:
  db:
    image: postgres
    container_name: shorts-db
    restart: always
    environment:
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: password
      POSTGRES_DB: shorts
    volumes:
      - pgdata:/var/lib/postgresql/data
    networks:
      - shorts-network

  adminer:
    image: adminer
    restart: always
    ports:
      - 4080:8080
    networks:
      - shorts-network

  redis:
    image: redis:latest
    restart: always
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
    networks:
      - shorts-network

  website:
    build:
      context: ./
      dockerfile: Dockerfile
    container_name: shorts-website
    volumes:
      - ./:/app/
    command: sh -c "npm install --verbose && npm run dev"
    ports:
      - 5002:5002
    networks:
      - shorts-network
    env_file:
      - path: ./.env
        required: true
    depends_on:
      - db
    restart: always

  drizzle_studio:
    build:
      context: ./
      dockerfile: src/database/drizzle/studio/Dockerfile
    container_name: drizzle-studio
    volumes:
      - ./:/app/
    ports:
      - 5003:5003
    networks:
      - shorts-network
    env_file:
      - path: ./.env
        required: true
    depends_on:
      - db
    restart: always

  jobs:
    build:
      context: ./
      dockerfile: src/jobs/Dockerfile
    container_name: shorts-jobs
    volumes:
      - ./:/app/
    networks:
      - shorts-network
    env_file:
      - path: ./.env
        required: true
    depends_on:
      - db
    restart: always

volumes:
  pgdata:

networks:
  shorts-network:
    driver: bridge
