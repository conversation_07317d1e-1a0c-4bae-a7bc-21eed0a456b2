import i18n, { type Resource } from "i18next";
import { initReactI18next } from "react-i18next";

export function initI18n(lang: string, resources: Resource) {
  i18n
    .use(initReactI18next) // passes i18n down to react-i18next
    .init({
      resources,
      lng: lang || "en",

      interpolation: {
        escapeValue: false, // react already safes from xss
      },
    });

  return i18n;
}
