import z from "zod";

export const featureSchema = z.discriminatedUnion("type", [
  z.object({
    type: z.literal("Premium templates"),
    meta: z.object({}),
  }),
  z.object({
    type: z.literal("24/7 priority support"),
    meta: z.object({
      responseTime: z.number(), // "hour"
      channels: z.array(z.enum(["chat", "email", "phone"])),
    }),
  }),
  z.object({
    type: z.literal("Advanced analytics"),
    meta: z.object({}),
  }),
  z.object({
    type: z.literal("Team collaboration"),
    meta: z.object({
      maxMembers: z.number().int(),
      roles: z.array(z.enum(["admin", "editor", "viewer"])),
    }),
  }),
  z.object({
    type: z.literal("API access"),
    meta: z.object({
      rateLimit: z.number(), // "req/min"
    }),
  }),
  z.object({
    type: z.literal("Custom integrations"),
    meta: z.object({}),
  }),
]);

export const features = featureSchema.def.options.map((x) => x.def.shape.type.value);

export type Feature = z.infer<typeof featureSchema>;
export type FeatureType = Feature["type"];
export type FeatureMeta<T extends FeatureType> = Extract<Feature, { type: T }>["meta"];
