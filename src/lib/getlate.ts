import {
  deleteGetlateAccount,
  getGetlateProfile,
  insertGetlateAccount,
  insertGetlateProfile,
} from "@/database/drizzle/queries/getlate";
import axios from "axios";
import { User } from "better-auth";

const GETLATE_BASE = process.env.GETLATE_DEV_BASE_URL;
const GETLATE_KEY = process.env.GETLATE_DEV_API_KEY;

export async function getOrCreateProfile(db: DB, user: User) {
  try {
    let getlateProfile = await getGetlateProfile(db, user.id);

    if (!getlateProfile) {
      const profileRes = await axios.post(
        `${GETLATE_BASE}/profiles`,
        {
          //name must be unique

          name: `${user.name || user.email?.split("@")[0]} ${user.id} Profile`,
          description: user.id,
          color: getRandomColor(),
        },
        { headers: { Authorization: `Bearer ${GETLATE_KEY}` } },
      );

      getlateProfile = {
        ...(
          await insertGetlateProfile(db, {
            userId: user.id,
            getlateProfileId: profileRes.data.profile._id,
          }).returning()
        )[0],
        accounts: [],
      };
    }
    return getlateProfile;
  } catch (error) {
    console.log(error);
    return null;
  }
}

export async function deleteProfile(db: DB, userId: string) {
  try {
    const getlateProfile = await getGetlateProfile(db, userId);
    if (!getlateProfile) return;

    await Promise.all(
      getlateProfile.accounts.map(async (account) => {
        if (account.getlateAccountId) {
          await disconnectFromAccount(db, account.getlateAccountId, getlateProfile.getlateProfileId);
        }
      }),
    );

    await axios.delete(`${GETLATE_BASE}/profiles/${getlateProfile.getlateProfileId}`, {
      headers: {
        Authorization: `Bearer ${GETLATE_KEY}`,
      },
    });
  } catch (error) {
    console.log(error);
    throw error;
  }
}

type Account = {
  _id: string;
  profileId: string;
  platform: string;
  username: string;
  displayName: string;
  profilePicture: string;
  isActive: boolean;
  tokenExpiresAt: string;
  permissions: string[];
};

export async function connectToAccount(db: DB, user: User, socialPlatform: string) {
  try {
    const platform = socialPlatform.toLowerCase();
    const getlateProfile = await getOrCreateProfile(db, user);

    const authUrlRes = await axios.get(`${GETLATE_BASE}/connect/${platform}`, {
      headers: { Authorization: `Bearer ${GETLATE_KEY}` },
      params: {
        profileId: getlateProfile?.getlateProfileId,
        redirect_url: `${process.env.BASE_URL}/account/social-accounts/`,
      },
    });

    return authUrlRes.data as { authUrl: string; state: string };
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function syncAccounts(db: DB, userId: string) {
  try {
    const dbGetlateProfile = await getGetlateProfile(db, userId);
    if (!dbGetlateProfile) return;

    const res = await axios.get(`${GETLATE_BASE}/accounts?profileId=${dbGetlateProfile.getlateProfileId}`, {
      headers: { Authorization: `Bearer ${GETLATE_KEY}` },
    });
    const remoteAccounts = (res?.data?.accounts || []) as Account[];

    const localIds = new Set(dbGetlateProfile.accounts.map((item) => item.getlateAccountId));
    const remoteIds = new Set(remoteAccounts.map((item: { _id: string }) => item._id));

    // Insert missing db entries
    await Promise.all(
      remoteAccounts.map(async (account) => {
        if (!localIds.has(account._id)) {
          await insertGetlateAccount(db, {
            profileId: dbGetlateProfile.id,
            getlateAccountId: account._id,
            platform: account.platform,
          });
        }
      }),
    );

    // Delete extra db entries
    await Promise.all(
      dbGetlateProfile.accounts.map(async (account) => {
        if (!remoteIds.has(account.getlateAccountId)) {
          await deleteGetlateAccount(db, account.getlateAccountId);
        }
      }),
    );

    return await getGetlateProfile(db, userId);
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function disconnectFromAccount(db: DB, accountId: string, profileId: string) {
  try {
    const userAccounts = await axios.get(`${GETLATE_BASE}/accounts?profileId=${profileId}`, {
      headers: {
        Authorization: `Bearer ${GETLATE_KEY}`,
      },
    });

    const platAcc = userAccounts?.data?.accounts.find((acc: any) => acc._id === accountId);

    if (platAcc) {
      await axios.delete(`${GETLATE_BASE}/accounts/${platAcc._id}`, {
        headers: {
          Authorization: `Bearer ${GETLATE_KEY}`,
        },
      });
      await deleteGetlateAccount(db, accountId);
    }
  } catch (error) {
    console.log(error);
    throw error;
  }
}

function getRandomColor() {
  const letters = "0123456789ABCDEF";
  let color = "#";
  for (let i = 0; i < 6; i++) {
    color += letters[Math.floor(Math.random() * 16)];
  }
  return color;
}

export type SharePostProps = {
  content: string;
  platforms: { platform: string; accountId: string }[];

  scheduledFor: string;
  timezone: string;
  publishNow?: boolean;
  isDraft?: boolean;
  visibility?: "public" | "private" | "unlisted";
  tags?: string[];
  mediaItems: [
    {
      type: "image" | "video" | "gif" | "document";
      url: string;
      filename?: string;
    },
  ];
};

export async function sharePost(props: SharePostProps) {
  return await axios.post(`${GETLATE_BASE}/posts`, props, { headers: { Authorization: `Bearer ${GETLATE_KEY}` } });
}
