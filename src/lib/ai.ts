import axios, { AxiosError } from "axios";
import z, { toJSONSchema } from "zod";

type StructuredOutputParams<T> = {
  prompt: string;
  schema: z.ZodType<T>;
  model: string;
  maxTokens?: number;
};

abstract class BaseAIService {
  abstract getStructuredOutput<T>(params: StructuredOutputParams<T>): Promise<{ output: T; searchResults?: any }>;
}

export class PerplexityAI implements BaseAIService {
  async getStructuredOutput<T>(params: StructuredOutputParams<T>) {
    const response = await axios
      .post(
        `https://api.perplexity.ai/chat/completions`,
        {
          model: params.model,
          messages: [
            {
              role: "user",
              content: params.prompt,
            },
          ],
          max_tokens: params.maxTokens,
          response_format: {
            type: "json_schema",
            json_schema: {
              schema: toJSONSchema(params.schema),
            },
          },
        },
        {
          headers: {
            Authorization: `Bearer ${process.env.PERPLEXITY_API_KEY}`,
            "Content-Type": "application/json",
          },
        },
      )
      .catch((e) => console.log((e as AxiosError).toJSON()));

    const output = JSON.parse(response?.data.choices[0].message.content) as z.infer<typeof params.schema>;

    return { output, searchResults: response?.data.search_results };
  }
}

export const ai = new PerplexityAI();
