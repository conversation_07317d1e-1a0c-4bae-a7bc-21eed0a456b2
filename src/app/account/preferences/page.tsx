"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Save } from "lucide-react";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

const initialSettings = {
  emailNotifications: true,
  pushNotifications: false,
  marketingEmails: true,
  twoFactorAuth: false,
  profileVisibility: true,
};

export default function Page() {
  const { t } = useTranslation();
  const [settings, setSettings] = useState(initialSettings);
  const [isLoading, setIsLoading] = useState(false);

  const handleSettingChange = (setting: string, value: boolean) => {
    setSettings((prev) => ({
      ...prev,
      [setting]: value,
    }));
  };

  const handleSaveSettings = async () => {
    setIsLoading(true);
    try {
      /**
       * TODO: Implement these settings
       */
      await new Promise((resolve) => setTimeout(resolve, 1000));
      console.log("Settings updated:", settings);
      toast.success(t("Settings saved successfully!"));
    } catch (error) {
      console.error("Error saving settings:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>{t("Notification Preferences")}</CardTitle>
          <CardDescription>{t("Choose how you want to be notified about account activity")}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>{t("Email Notifications")}</Label>
              <p className="text-sm text-gray-600">{t("Receive notifications via email")}</p>
            </div>
            <Switch
              checked={settings.emailNotifications}
              onCheckedChange={(checked) => handleSettingChange("emailNotifications", checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>{t("Push Notifications")}</Label>
              <p className="text-sm text-gray-600">{t("Receive push notifications in your browser")}</p>
            </div>
            <Switch
              checked={settings.pushNotifications}
              onCheckedChange={(checked) => handleSettingChange("pushNotifications", checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>{t("Marketing Emails")}</Label>
              <p className="text-sm text-gray-600">{t("Receive emails about new features and updates")}</p>
            </div>
            <Switch
              checked={settings.marketingEmails}
              onCheckedChange={(checked) => handleSettingChange("marketingEmails", checked)}
            />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>{t("Privacy Settings")}</CardTitle>
          <CardDescription>{t("Control your privacy and profile visibility")}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>{t("Public Profile")}</Label>
              <p className="text-sm text-gray-600">{t("Make your profile visible to other users")}</p>
            </div>
            <Switch
              checked={settings.profileVisibility}
              onCheckedChange={(checked) => handleSettingChange("profileVisibility", checked)}
            />
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-end">
        <Button onClick={handleSaveSettings} disabled={isLoading} loading={isLoading}>
          <Save className="h-4 w-4 mr-2" />
          {t("Save Preferences")}
        </Button>
      </div>
    </>
  );
}
