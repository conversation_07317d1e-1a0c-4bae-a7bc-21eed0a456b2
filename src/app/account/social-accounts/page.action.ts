"use server";

import { errors } from "@/lib/errors";
import { getDataContext } from "@/lib/getDataContext";
import { connectToAccount, disconnectFromAccount, syncAccounts } from "@/lib/getlate";

export async function onGetConnectionLink(socialPlatform: string) {
  const context = await getDataContext();

  if (!context.session?.user) return { error: errors.NotAuthenticated };

  return await connectToAccount(context.db, context.session?.user, socialPlatform);
}

export async function onSyncGetlateAccounts() {
  const context = await getDataContext();

  if (!context.session?.user) return { error: errors.NotAuthenticated };

  return await syncAccounts(context.db, context.session?.user.id);
}

export async function onDisconnectAccount(accountId: string, profileId: string) {
  const context = await getDataContext();

  if (!context.session?.user) return { error: errors.NotAuthenticated };

  return await disconnectFromAccount(context.db, accountId, profileId);
}
