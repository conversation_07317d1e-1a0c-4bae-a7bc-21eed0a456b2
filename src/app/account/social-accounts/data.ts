"use server";

import { getDataContext } from "@/lib/getDataContext";
import { getOrCreateProfile } from "@/lib/getlate";

export type Data = Awaited<ReturnType<typeof getData>>;

export async function getData() {
  const { db, session } = await getDataContext();
  if (!session?.user) throw new Error("Not authenticated");
  const getlateProfile = await getOrCreateProfile(db, session?.user);

  return { getlateProfile };
}
