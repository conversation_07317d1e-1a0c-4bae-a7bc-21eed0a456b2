"use server";

import * as translationQueries from "@/database/drizzle/queries/translations";
import { getDataContext } from "@/lib/getDataContext";

export type Data = Awaited<ReturnType<typeof getData>>;

export async function getData() {
  const { db, session } = await getDataContext();
  if (!session?.user) throw new Error("Not authenticated");
  const translations = await translationQueries.getTranslationsFromDb(db);

  return { translations };
}
