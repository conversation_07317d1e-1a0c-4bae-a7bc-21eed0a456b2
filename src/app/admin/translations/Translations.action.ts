"use server";

import * as translationQueries from "@/database/drizzle/queries/translations";
import { TranslationInsert } from "@/database/drizzle/schema/translations";
import { errors } from "@/lib/errors";
import { getDataContext } from "@/lib/getDataContext";

export async function updateTranslation(data: TranslationInsert) {
  const { db, session } = await getDataContext();

  if (!session?.user) {
    return { error: errors.NotAuthenticated };
  }

  const res = await translationQueries.updateTranslation(db, data);

  return res;
}

export async function deleteTranslation(id: string) {
  const { db, session } = await getDataContext();

  if (!session?.user) {
    return { error: errors.NotAuthenticated };
  }

  const res = await translationQueries.deleteTranslation(db, id);

  return res;
}
