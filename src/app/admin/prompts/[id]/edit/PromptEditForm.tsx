"use client";

import { <PERSON>, SelectField, TextareaField } from "@/components/Field";
import { Heading } from "@/components/Heading";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Form } from "@/components/ui/form";
import { Separator } from "@/components/ui/separator";
import { PromptItem } from "@/database/drizzle/schema/prompts";
import { prompts } from "@/lib/prompts";
import { tf } from "@/lib/tf";
import { zodResolver } from "@hookform/resolvers/zod";
import { ArrowLeftIcon, RotateCcwIcon, SaveIcon } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState, useTransition } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";
import z from "zod";
import { onResetPrompt, onUpdatePrompt } from "./actions";

interface Props {
  id: keyof typeof prompts;
  prompt: PromptItem;
  defaultPrompt: Omit<(typeof prompts)[keyof typeof prompts], "schema">;
}

const formSchema = z.object({
  model: z.string().nonempty(),
  prompt: z.string().nonempty(),
  maxTokens: z.number().min(1).max(100000),
});

export default function PromptEditForm({ id, prompt, defaultPrompt }: Props) {
  const { t } = useTranslation();
  const [isPending, startTransition] = useTransition();
  const [isResetting, setIsResetting] = useState(false);
  const router = useRouter();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      model: prompt.model,
      prompt: prompt.prompt,
      maxTokens: prompt.maxTokens,
    },
  });

  const hasChanges = form.formState.isDirty;

  const isDefault =
    prompt.model === defaultPrompt.model &&
    prompt.prompt === defaultPrompt.prompt &&
    prompt.maxTokens === defaultPrompt.maxTokens;

  const handleSubmit = async (formData: z.infer<typeof formSchema>) => {
    startTransition(async () => {
      await tf(onUpdatePrompt, id, formData);
    });
  };

  const handleReset = async () => {
    setIsResetting(true);
    try {
      const result = await tf(onResetPrompt, id);
      if (result) {
        form.reset({
          model: defaultPrompt.model,
          prompt: defaultPrompt.prompt,
          maxTokens: defaultPrompt.maxTokens,
        });
        toast.success(t("Prompt reset to default values"));
        router.refresh();
      }
    } finally {
      setIsResetting(false);
    }
  };

  return (
    <div className="space-y-6">
      <Heading
        title={
          <>
            {t("Edit")} {id}
          </>
        }
        description={t("Modify prompt configuration and template")}
        actions={
          <>
            <Button variant="outline" size="sm" asChild>
              <Link href="/admin/prompts">
                <ArrowLeftIcon className="h-4 w-4 mr-2" />
                {t("Back to Prompts")}
              </Link>
            </Button>
            {!isDefault && (
              <Button variant="outline" onClick={handleReset} disabled={isResetting}>
                <RotateCcwIcon className="h-4 w-4 mr-2" />
                {t("Reset to Default")}
              </Button>
            )}
          </>
        }
      />

      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>{t("Configuration")}</CardTitle>
              <CardDescription>{t("Basic prompt settings and metadata")}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <SelectField
                  control={form.control}
                  name="model"
                  label={t("Model")}
                  options={[
                    { label: "Sonar", value: "sonar" },
                    { label: "Sonar pro", value: "sonar-pro" },
                    { label: "Sonar deep research", value: "sonar-deep-research" },
                    { label: "Sonar reasoning", value: "sonar-reasoning" },
                    { label: "Sonar reasoning pro", value: "sonar-reasoning-pro" },
                  ]}
                />
                <Field
                  control={form.control}
                  name="maxTokens"
                  label={t("Max Tokens")}
                  input={{ props: { type: "number", min: 1, max: 100000 } }}
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>{t("Prompt Template")}</CardTitle>
              <CardDescription>
                {t("The template used for generating AI prompts. Use Handlebars syntax for parameters.")}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <TextareaField
                control={form.control}
                name="prompt"
                label={t("Template")}
                input={{
                  props: {
                    rows: 5,
                    placeholder: t("Enter your prompt template..."),
                    className: "min-h-[200px] font-mono",
                  },
                }}
              />
            </CardContent>
          </Card>

          {defaultPrompt.paramDescriptions && Object.keys(defaultPrompt.paramDescriptions).length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>{t("Available Parameters")}</CardTitle>
                <CardDescription>{t("Parameters that can be used in the template")}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Object.entries(defaultPrompt.paramDescriptions).map(([param, description]) => (
                    <div key={param} className="border-l-4 border-primary/20 pl-4">
                      <div className="flex items-center gap-2 mb-1">
                        <Badge variant="outline" className="font-mono text-xs">
                          {`{{${param}}}`}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">{description}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          <Separator />

          <div className="flex justify-end gap-3">
            <Button type="button" variant="outline" asChild>
              <Link href={`/admin/prompts/${id}`}>{t("Cancel")}</Link>
            </Button>
            <Button type="submit" disabled={!hasChanges || isPending} loading={isPending}>
              <SaveIcon className="h-4 w-4 mr-2" />
              {t("Save Changes")}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
