import { getOrCreatePrompt } from "@/database/drizzle/queries/prompts";
import { getDataContext } from "@/lib/getDataContext";
import { prompts } from "@/lib/prompts";
import { notFound, redirect } from "next/navigation";
import PromptEditForm from "./PromptEditForm";

interface Props {
  params: Promise<{ id: string }>;
}

export default async function PromptEditPage({ params }: Props) {
  const { id } = await params;

  if (!(id in prompts)) {
    notFound();
  }

  const context = await getDataContext();

  if (!context.session?.user) {
    redirect("/login");
  }

  if (context.session.user.role !== "admin") {
    redirect("/");
  }

  // Get or create the prompt with default values
  const prompt = await getOrCreatePrompt(context.db, id as keyof typeof prompts);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { schema, ...defaultPrompt } = prompts[id as keyof typeof prompts];

  return (
    <div className="container mx-auto py-6">
      <PromptEditForm id={id as keyof typeof prompts} prompt={prompt} defaultPrompt={defaultPrompt} />
    </div>
  );
}
