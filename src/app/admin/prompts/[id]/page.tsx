import { Heading } from "@/components/Heading";
import T from "@/components/T";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { prompts } from "@/lib/prompts";
import { zx } from "@traversable/zod";
import { ArrowLeftIcon, EditIcon } from "lucide-react";
import Link from "next/link";
import { notFound } from "next/navigation";

interface Props {
  params: Promise<{ id: string }>;
}

export default async function PromptViewPage({ params }: Props) {
  const { id } = await params;

  if (!(id in prompts)) {
    notFound();
  }

  const prompt = prompts[id as keyof typeof prompts];

  return (
    <div className="container mx-auto py-6 space-y-6">
      <Heading
        title={id}
        description={<T text="Prompt details and configuration" />}
        actions={
          <>
            <Button variant="outline" size="sm" asChild>
              <Link href="/admin/prompts">
                <ArrowLeftIcon className="h-4 w-4 mr-2" />
                <T text="Back to Prompts" />
              </Link>
            </Button>
            <Button asChild>
              <Link href={`/admin/prompts/${id}/edit`}>
                <EditIcon className="h-4 w-4 mr-2" />
                <T text="Edit Prompt" />
              </Link>
            </Button>
          </>
        }
      />

      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <CardTitle>
              <T text="Configuration" />
            </CardTitle>
            <CardDescription>
              <T text="Basic prompt settings and metadata" />
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  <T text="Model" />
                </label>
                <div className="mt-1">
                  <Badge variant="secondary">{prompt.model}</Badge>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  <T text="Max Tokens" />
                </label>
                <div className="mt-1">
                  <Badge variant="outline">{prompt.maxTokens}</Badge>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  <T text="Prompt ID" />
                </label>
                <div className="mt-1">
                  <code className="bg-muted px-2 py-1 rounded text-sm">{id}</code>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>
              <T text="Prompt Template" />
            </CardTitle>
            <CardDescription>
              <T text="The template used for generating AI prompts" />
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="bg-muted p-4 rounded-md">
              <pre className="text-sm font-mono whitespace-pre-wrap">{prompt.prompt}</pre>
            </div>
          </CardContent>
        </Card>

        {prompt.paramDescriptions && Object.keys(prompt.paramDescriptions).length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>
                <T text="Parameters" />
              </CardTitle>
              <CardDescription>
                <T text="Available template parameters and their descriptions" />
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(prompt.paramDescriptions).map(([param, description]) => (
                  <div key={param} className="border-l-4 border-primary/20 pl-4">
                    <div className="flex items-center gap-2 mb-1">
                      <code className="bg-muted px-2 py-1 rounded text-sm font-mono">{`{{${param}}}`}</code>
                    </div>
                    <p className="text-sm text-muted-foreground">{description}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {prompt.schema && (
          <Card>
            <CardHeader>
              <CardTitle>
                <T text="Response Schema" />
              </CardTitle>
              <CardDescription>
                <T text="Expected structure of the AI response" />
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-muted p-4 rounded-md">
                <pre className="text-sm font-mono">{zx.toType(prompt.schema)}</pre>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
