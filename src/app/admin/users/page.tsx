"use client";

import {
  Pa<PERSON>ation,
  SearchAndFilter,
  useFilterAndPaginationParams,
  useUpdateSearchParams,
} from "@/components/Filter-Pagination";
import { Heading } from "@/components/Heading";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { authClient } from "@/lib/auth-client";
import { UserWithRole } from "better-auth/plugins";
import { EditIcon, SaveIcon, XIcon } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

interface EditingUser {
  id: string;
  role: "user" | "admin";
  password: string;
}

export default function UsersPage() {
  const { t } = useTranslation();

  const [users, setUsers] = useState<UserWithRole[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(true);
  const [editingUser, setEditingUser] = useState<EditingUser | null>(null);
  const [savingUser, setSavingUser] = useState<string | null>(null);

  const { searchValue, searchField, sortBy, sortDirection, limit, offset } = useFilterAndPaginationParams();

  const updateSearchParams = useUpdateSearchParams();

  // Fetch users
  const fetchUsers = useCallback(async () => {
    try {
      setLoading(true);

      const query: Record<string, string | number> = {
        limit,
        offset,
        sortBy,
        sortDirection,
      };

      if (searchValue) {
        query.searchValue = searchValue;
        query.searchField = searchField;
        query.searchOperator = "contains";
      }

      const { data, error } = await authClient.admin.listUsers({ query });

      if (error) {
        console.error("Error fetching users:", error);
        toast.error(t("Failed to fetch users"));
        return;
      }

      if (data) {
        setUsers(data.users);
        setTotal(data.total);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
      toast.error(t("Failed to fetch users"));
    } finally {
      setLoading(false);
    }
  }, [limit, offset, sortBy, sortDirection, searchValue, searchField, t]);

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  // Handle role update
  const handleRoleUpdate = async (userId: string, newRole: "user" | "admin") => {
    try {
      setSavingUser(userId);

      const { error } = await authClient.admin.setRole({
        userId,
        role: newRole,
      });

      if (error) {
        console.log("Error updating role:", error);
        toast.error(t("Failed to update role"));
        return;
      }

      toast.success(t("Role updated successfully"));
      await fetchUsers(); // Refresh the list
      setEditingUser(null);
    } catch (error) {
      console.log("Error updating role:", error);
      toast.error(t("Failed to update role"));
    } finally {
      setSavingUser(null);
    }
  };

  // Handle password update
  const handlePasswordUpdate = async (userId: string, newPassword: string) => {
    if (!newPassword.trim()) {
      toast.error(t("Password cannot be empty"));
      return;
    }

    try {
      setSavingUser(userId);

      const { error } = await authClient.admin.setUserPassword({
        userId,
        newPassword,
      });

      if (error) {
        console.error("Error updating password:", error);
        toast.error(t("Failed to update password"));
        return;
      }

      toast.success(t("Password updated successfully"));
      setEditingUser(null);
    } catch (error) {
      console.error("Error updating password:", error);
      toast.error(t("Failed to update password"));
    } finally {
      setSavingUser(null);
    }
  };

  // Handle save changes
  const handleSaveChanges = async () => {
    if (!editingUser) return;

    const { id, role, password } = editingUser;

    // Update role if changed
    const currentUser = users.find((u) => u.id === id);
    if (currentUser && role !== currentUser.role) {
      await handleRoleUpdate(id, role);
    }

    // Update password if provided
    if (password.trim()) {
      await handlePasswordUpdate(id, password);
    }

    if (!role || role === currentUser?.role) {
      setEditingUser(null);
    }
  };

  const startEditing = (user: UserWithRole) => {
    setEditingUser({
      id: user.id,
      role: (user.role as any) || "user",
      password: "",
    });
  };

  const cancelEditing = () => {
    setEditingUser(null);
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <Heading title={t("Users Management")} description={t("Manage user accounts, roles, and permissions")} />

      {/* Search and Filters */}
      <SearchAndFilter
        searchFields={[
          { label: t("Email"), value: "email" },
          { label: t("Name"), value: "name" },
        ]}
        sortFields={[
          { label: t("Created Date"), value: "createdAt" },
          { label: t("Name"), value: "name" },
          { label: t("Email"), value: "email" },
          { label: t("Role"), value: "role" },
        ]}
      />

      {/* Users Table */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>
              {t("Users")} ({total})
            </CardTitle>
            <div className="flex items-center gap-2">
              <Label>{t("Per page:")}</Label>
              <Select
                value={limit.toString()}
                onValueChange={(value) => updateSearchParams({ limit: parseInt(value), page: 1 })}
              >
                <SelectTrigger className="w-20">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5</SelectItem>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="25">25</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">{t("Loading...")}</div>
          ) : users.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">{t("No users found")}</div>
          ) : (
            <div className="space-y-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t("Name")}</TableHead>
                    <TableHead>{t("Email")}</TableHead>
                    <TableHead>{t("Role")}</TableHead>
                    <TableHead>{t("Status")}</TableHead>
                    <TableHead>{t("Created")}</TableHead>
                    <TableHead>{t("Actions")}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {users.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell className="font-medium">{user.name}</TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>
                        {editingUser?.id === user.id ? (
                          <Select
                            value={editingUser.role}
                            onValueChange={(value) => setEditingUser({ ...editingUser, role: value as any })}
                          >
                            <SelectTrigger className="w-32">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="user">{t("User")}</SelectItem>
                              <SelectItem value="admin">{t("Admin")}</SelectItem>
                            </SelectContent>
                          </Select>
                        ) : (
                          <Badge variant={user.role === "admin" ? "default" : "secondary"}>{user.role}</Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        {user.banned ? (
                          <Badge variant="destructive">{t("Banned")}</Badge>
                        ) : (
                          <Badge variant="outline">{t("Active")}</Badge>
                        )}
                      </TableCell>
                      <TableCell>{new Date(user.createdAt).toLocaleDateString()}</TableCell>
                      <TableCell>
                        {editingUser?.id === user.id ? (
                          <div className="flex items-center gap-2">
                            {/* <Input
                              type="password"
                              placeholder={t("New password (optional)")}
                              value={editingUser.password}
                              onChange={(e) => setEditingUser({ ...editingUser, password: e.target.value })}
                              className="w-40"
                            /> */}
                            <Button size="sm" onClick={handleSaveChanges} disabled={savingUser === user.id}>
                              <SaveIcon className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={cancelEditing}
                              disabled={savingUser === user.id}
                            >
                              <XIcon className="h-4 w-4" />
                            </Button>
                          </div>
                        ) : (
                          <Button
                            type="button"
                            size="sm"
                            variant="outline"
                            onClick={() => startEditing(user)}
                            disabled={!!editingUser}
                          >
                            <EditIcon className="h-4 w-4" />
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              <Pagination total={total} />
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
