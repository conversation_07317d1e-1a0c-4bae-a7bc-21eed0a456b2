import { getDataContext } from "@/lib/getDataContext";
import { redirect } from "next/navigation";
import { PackageCreateForm } from "./PackageCreateForm";

export default async function PackageCreatePage() {
  const { session } = await getDataContext();

  if (!session?.user) {
    redirect("/login");
  }

  if (session.user.role !== "admin") {
    redirect("/");
  }

  return (
    <div className="container mx-auto py-6">
      <PackageCreateForm />
    </div>
  );
}
