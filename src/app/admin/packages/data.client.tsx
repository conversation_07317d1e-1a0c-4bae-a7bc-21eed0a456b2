"use client";

import { createContext, useContext } from "react";
import type { Data } from "./data";

const DataContext = createContext<Data | null>(null);

export function DataContainer({ children, data }: { children: React.ReactNode; data: Data }) {
  return <DataContext.Provider value={data}>{children}</DataContext.Provider>;
}

export function useData() {
  const data = useContext(DataContext);
  if (!data) {
    throw new Error("useData must be used within a DataContainer");
  }
  return data;
}
