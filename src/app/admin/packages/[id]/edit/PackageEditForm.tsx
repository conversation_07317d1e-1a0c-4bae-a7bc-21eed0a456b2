"use client";

import { <PERSON>boxField, Field, SelectField, TextareaField } from "@/components/Field";
import { Heading } from "@/components/Heading";
import T from "@/components/T";
import { <PERSON>ton } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Form } from "@/components/ui/form";
import { PackageItem } from "@/database/drizzle/schema/packages";
import { tf } from "@/lib/tf";
import { zodResolver } from "@hookform/resolvers/zod";
import { ArrowLeftIcon } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";
import { z } from "zod";
import { onUpdatePackage } from "./updatePackage.action";

const packageSchema = z.object({
  name: z.string().nonempty("Name is required"),
  description: z.string().optional(),
  price: z.string().refine((val) => !isNaN(parseFloat(val)) && parseFloat(val) >= 0, "Price must be a valid number"),
  currency: z.string(),
  billingInterval: z.enum(["month", "year"]),
  features: z.string().optional(), // We'll handle this as a comma-separated string
  isActive: z.boolean(),
  maxVideos: z.string().optional(),
  maxStorageGB: z.string().optional(),
  priority: z.string().refine((val) => !isNaN(parseInt(val)), "Priority must be a valid number"),
});

type PackageFormData = z.infer<typeof packageSchema>;

interface PackageEditFormProps {
  packageData: PackageItem;
}

export function PackageEditForm({ packageData }: PackageEditFormProps) {
  const { t } = useTranslation();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<PackageFormData>({
    resolver: zodResolver(packageSchema),
    defaultValues: {
      name: packageData.name,
      description: packageData.description || "",
      price: packageData.price,
      currency: packageData.currency,
      billingInterval: packageData.billingInterval as "month" | "year",
      isActive: packageData.isActive,
      maxVideos: packageData.maxVideos?.toString() || "",
      maxStorageGB: packageData.maxStorageGB?.toString() || "",
      priority: packageData.priority.toString(),
    },
  });

  const onSubmit = async (values: PackageFormData) => {
    setIsLoading(true);

    // Process features from comma-separated string to array
    const features = values.features
      ? values.features
          .split(",")
          .map((f) => f.trim())
          .filter((f) => f.length > 0)
      : [];

    const updateData = {
      ...values,
      features,
      maxVideos: values.maxVideos ? parseInt(values.maxVideos) : null,
      maxStorageGB: values.maxStorageGB ? parseInt(values.maxStorageGB) : null,
      priority: parseInt(values.priority),
    };

    const result = await tf(onUpdatePackage, { packageId: packageData.id, data: updateData });

    if (result) {
      toast.success(t("Package updated successfully"));
      router.push(`/admin/packages/${packageData.id}`);
    }

    setIsLoading(false);
  };

  return (
    <div className="space-y-6">
      <Heading
        title={<T text="Edit Package" />}
        description={packageData.name}
        actions={
          <Button variant="outline" size="sm" asChild>
            <Link href={`/admin/packages/${packageData.id}`}>
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              <T text="Back to Package" />
            </Link>
          </Button>
        }
      />

      <Card>
        <CardHeader>
          <CardTitle>
            <T text="Package Information" />
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                <Field
                  name="name"
                  label={t("Package Name")}
                  control={form.control}
                  input={{ props: { placeholder: t("Enter package name") } }}
                />
                <Field
                  name="priority"
                  label={t("Priority")}
                  control={form.control}
                  input={{ props: { type: "number", placeholder: t("Enter priority (higher = shown first)") } }}
                />
              </div>

              <TextareaField
                name="description"
                label={t("Description")}
                control={form.control}
                input={{
                  props: { placeholder: t("Enter package description") },
                }}
              />

              <div className="grid gap-6 md:grid-cols-3">
                <Field
                  name="price"
                  label={t("Price")}
                  control={form.control}
                  input={{ props: { type: "number", step: "0.01", placeholder: "0.00" } }}
                />
                <SelectField
                  name="currency"
                  label={t("Currency")}
                  control={form.control}
                  options={[
                    { value: "usd", label: "USD" },
                    { value: "eur", label: "EUR" },
                    { value: "gbp", label: "GBP" },
                  ]}
                />
                <SelectField
                  name="billingInterval"
                  label={t("Billing Interval")}
                  control={form.control}
                  options={[
                    { value: "month", label: t("Monthly") },
                    { value: "year", label: t("Yearly") },
                  ]}
                />
              </div>

              <div className="grid gap-6 md:grid-cols-2">
                <Field
                  name="maxVideos"
                  label={t("Max Videos per Period")}
                  control={form.control}
                  input={{ props: { type: "number", placeholder: t("Leave empty for unlimited") } }}
                />
                <Field
                  name="maxStorageGB"
                  label={t("Max Storage (GB)")}
                  control={form.control}
                  input={{ props: { type: "number", placeholder: t("Leave empty for unlimited") } }}
                />
              </div>

              <CheckboxField name="isActive" label={t("Active")} control={form.control} />

              <div className="flex gap-4">
                <Button type="submit" disabled={isLoading} loading={isLoading}>
                  <T text="Update Package" />
                </Button>
                <Button type="button" variant="outline" asChild>
                  <Link href={`/admin/packages/${packageData.id}`}>
                    <T text="Cancel" />
                  </Link>
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
