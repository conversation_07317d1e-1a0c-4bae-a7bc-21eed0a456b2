"use server";

import * as packageQueries from "@/database/drizzle/queries/packages";
import { errors } from "@/lib/errors";
import { getDataContext } from "@/lib/getDataContext";

interface TogglePackageStatusParams {
  packageId: string;
  isActive: boolean;
}

export async function onTogglePackageStatus(params: TogglePackageStatusParams) {
  const { db, session } = await getDataContext();

  if (!session?.user) return { error: errors.NotAuthenticated };
  if (session.user.role !== "admin") return { error: errors.NotAuthorized };

  const { packageId, isActive } = params;

  try {
    const [updatedPackage] = await packageQueries.togglePackageStatus(db, packageId, isActive);

    if (!updatedPackage) {
      return { error: errors.PackageNotFound };
    }

    return { success: true, package: updatedPackage };
  } catch (error) {
    console.error("Failed to toggle package status:", error);
    return { error: errors.InternalServerError };
  }
}
