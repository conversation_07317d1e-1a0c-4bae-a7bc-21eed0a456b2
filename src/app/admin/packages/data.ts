"use server";

import * as packageQueries from "@/database/drizzle/queries/packages";
import * as subscriptionQueries from "@/database/drizzle/queries/subscriptions";
import { getDataContext } from "@/lib/getDataContext";

export type Data = Awaited<ReturnType<typeof getData>>;

export async function getData() {
  const { db } = await getDataContext();

  // Get all packages (including inactive ones for admin)
  const packages = await packageQueries.getAllPackagesForAdmin(db);

  // Get subscription statistics
  const totalSubscriptions = await subscriptionQueries.getTotalSubscriptionsCount(db);
  const activeSubscriptions = await subscriptionQueries.getActiveSubscriptionsCount(db);

  return {
    packages,
    subscriptionStats: {
      total: totalSubscriptions[0]?.count || 0,
      active: activeSubscriptions[0]?.count || 0,
    },
  };
}
