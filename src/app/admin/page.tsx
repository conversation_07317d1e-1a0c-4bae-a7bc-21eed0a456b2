import T from "@/components/T";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { CreditCardIcon, FileBoxIcon, LanguagesIcon, UsersIcon } from "lucide-react";
import Link from "next/link";

export default function Page() {
  return (
    <div className="grid gap-6 grid-cols-[repeat(auto-fill,minmax(300px,1fr))]">
      <Card>
        <CardHeader>
          <div className="flex items-center gap-4">
            <FileBoxIcon className="h-12 w-12 text-muted-foreground" />
            <CardTitle>
              <T text="Prompts" />
            </CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <p>
            <T text="Manage AI prompts used throughout the application" />
          </p>
        </CardContent>
        <CardFooter>
          <Button asChild variant={"outline"}>
            <Link href="/admin/prompts">
              <T text="View" />
            </Link>
          </Button>
        </CardFooter>
      </Card>

      <Card>
        <CardHeader>
          <div className="flex items-center gap-4">
            <CreditCardIcon className="h-12 w-12 text-muted-foreground" />
            <CardTitle>
              <T text="Packages" />
            </CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <p>
            <T text="Manage subscription packages, pricing, and billing" />
          </p>
        </CardContent>
        <CardFooter>
          <Button asChild variant={"outline"}>
            <Link href="/admin/packages">
              <T text="View" />
            </Link>
          </Button>
        </CardFooter>
      </Card>

      <Card>
        <CardHeader>
          <div className="flex items-center gap-4">
            <UsersIcon className="h-12 w-12 text-muted-foreground" />
            <CardTitle>
              <T text="Users" />
            </CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <p>
            <T text="Manage user accounts, roles, and permissions" />
          </p>
        </CardContent>
        <CardFooter>
          <Button asChild variant={"outline"}>
            <Link href="/admin/users">
              <T text="View" />
            </Link>
          </Button>
        </CardFooter>
      </Card>

      <Card>
        <CardHeader>
          <div className="flex items-center gap-4">
            <LanguagesIcon className="h-12 w-12 text-muted-foreground" />
            <CardTitle>
              <T text="Translations" />
            </CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <p>
            <T text="Manage translations and localization settings" />
          </p>
        </CardContent>
        <CardFooter>
          <Button asChild variant={"outline"}>
            <Link href="/admin/translations">
              <T text="View" />
            </Link>
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
