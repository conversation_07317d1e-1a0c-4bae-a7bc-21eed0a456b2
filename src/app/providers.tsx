"use client";

import { useMemo } from "react";
import { I18nextProvider } from "react-i18next";

import { Toaster } from "@/components/ui/sonner";
import type { auth } from "@/lib/auth";
import { initI18n } from "@/lib/i18n";
import { PageContext } from "@/lib/page-context";
import { Resource } from "i18next";

export default function Provider({
  children,
  lang,
  session,
  translations,
}: {
  children: React.ReactNode;
  lang: string;
  session: Awaited<ReturnType<typeof auth.api.getSession>>;
  translations: Resource;
}) {
  const i18n = useMemo(() => initI18n(lang ?? window?.Cookies.get("lang") ?? "en", translations), [lang, translations]);

  return (
    <I18nextProvider i18n={i18n}>
      <PageContext value={{ lang, session }}>{children}</PageContext>
      <Toaster position="top-center" />
    </I18nextProvider>
  );
}
