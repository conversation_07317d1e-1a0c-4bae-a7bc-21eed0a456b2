"use client";

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "@/components/Field";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>T<PERSON>le, DialogTrigger } from "@/components/ui/dialog";
import { Form } from "@/components/ui/form";
import { VideoItem } from "@/database/drizzle/schema/videos";
import { tf } from "@/lib/tf";
import { zodResolver } from "@hookform/resolvers/zod";
import { EditIcon, ShareIcon } from "lucide-react";
import Link from "next/link";
import { useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import z from "zod";
import { platforms } from "../account/social-accounts/platforms";
import { onShare } from "./VideoList.action";
import { useData } from "./data.client";

export function VideoList({ initialVideoItems }: { initialVideoItems: VideoItem[] }) {
  const { t } = useTranslation();
  const [videoItems] = useState(initialVideoItems);

  return (
    <>
      <h1 className="mb-4 text-2xl font-bold">{t("Videos")}</h1>
      <div className="grid grid-cols-[repeat(auto-fill,minmax(300px,1fr))] gap-6">
        {videoItems.map((videoItem) => (
          <div key={videoItem.id} className="rounded-2xl overflow-clip">
            <video src={videoItem.videoUrl!} controls className="aspect-[9/16] bg-gray-50"></video>
            <div className="p-3 flex gap-3 items-center">
              <span>{videoItem.target}</span>
              <Button asChild className="ms-auto">
                <Link href={`/videos/${videoItem.id}`}>
                  <EditIcon /> <span className="sr-only">{t("Edit")}</span>
                </Link>
              </Button>
              <ShareDialog videoId={videoItem.id} />
            </div>
          </div>
        ))}
      </div>
    </>
  );
}

const formSchema = z.object({
  platforms: z.array(z.boolean()).length(platforms.length),
  scheduleAt: z.string(),
});

function toLocalISOString(date: Date) {
  const localDate = new Date(date.getTime() - date.getTimezoneOffset() * 60000); //offset in milliseconds. Credit https://stackoverflow.com/questions/10830357/javascript-toisostring-ignores-timezone-offset

  // Optionally remove second/millisecond if needed
  localDate.setSeconds(0);
  localDate.setMilliseconds(0);
  return localDate.toISOString().slice(0, -1);
}

function ShareDialog({ videoId }: { videoId: string }) {
  const { t } = useTranslation();
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      platforms: platforms.map(() => false),
      scheduleAt: toLocalISOString(new Date()),
    },
  });
  const { getlateProfile } = useData();
  const [postNow, setPostNow] = useState<"indeterminate" | boolean>(true);
  const [isSharing, setIsSharing] = useState(false);
  const accountsMap = useMemo(() => {
    return new Map((getlateProfile?.accounts || []).map((x) => [x.platform.toLowerCase(), x]));
  }, [getlateProfile?.accounts]);

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button>
          <ShareIcon />
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t("Share")}</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(async function onSubmit(values: z.infer<typeof formSchema>) {
              setIsSharing(true);
              await tf(
                onShare,
                videoId,
                platforms.filter((x, i) => values.platforms[i]).map((x) => x.name),
                postNow === true ? undefined : new Date(values.scheduleAt).toISOString(),
              );
              setIsSharing(false);
            })}
            className="space-y-4"
          >
            {platforms.map((platform) => (
              <CheckboxField
                key={platform.name}
                name={`platforms.${platforms.indexOf(platform)}`}
                label={
                  <>
                    {<platform.icon style={{ color: platform.color }} />} {platform.name}
                  </>
                }
                disabled={!accountsMap.has(platform.name.toLowerCase())}
              />
            ))}
            <label className="flex gap-2">
              <Checkbox checked={postNow} onCheckedChange={setPostNow} /> {t("Post now")}
            </label>
            <Field
              name="scheduleAt"
              input={{ props: { type: "datetime-local" } }}
              label="Schedule at"
              disabled={postNow === true}
            />
            <Button type="submit" disabled={isSharing} loading={isSharing}>
              {t("Share")}
            </Button>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
