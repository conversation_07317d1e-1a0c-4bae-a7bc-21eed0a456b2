"use server";

import { getVideo } from "@/database/drizzle/queries/videos";
import { getVoices } from "@/lib/elevenlabs";
import { getDataContext } from "@/lib/getDataContext";

export type Data = Awaited<ReturnType<typeof getData>>;

export const getData = async ({ params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params;
  const { db } = await getDataContext();
  const [video] = await getVideo(db, id);
  const voices = await getVoices();

  return { video, voices };
};
