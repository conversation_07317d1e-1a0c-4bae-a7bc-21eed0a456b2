import { dbInit } from "@/database/drizzle/db";
import * as packageQueries from "@/database/drizzle/queries/packages";
import * as subscriptionQueries from "@/database/drizzle/queries/subscriptions";
import { NextRequest } from "next/server";
import type Stripe from "stripe";

import { constructWebhookEvent, STRIPE_WEBHOOK_EVENTS } from "@/lib/stripe";
import { createOrGetStripeCustomer } from "@/lib/subscription-service";

export const runtime = "nodejs";
export const dynamic = "force-dynamic";

export async function POST(req: NextRequest) {
  try {
    const body = await req.text();
    const signature = req.headers.get("stripe-signature");

    if (!signature) {
      console.error("Missing stripe-signature header");
      return Response.json({ error: "Missing stripe-signature header" }, { status: 400 });
    }

    const event = constructWebhookEvent(body, signature);
    const db = dbInit();

    console.log(`Received Stripe webhook: ${event.type}`);

    switch (event.type) {
      case STRIPE_WEBHOOK_EVENTS.CUSTOMER_SUBSCRIPTION_CREATED:
        await handleSubscriptionCreated(db, event.data.object as Stripe.Subscription);
        break;

      case STRIPE_WEBHOOK_EVENTS.CUSTOMER_SUBSCRIPTION_UPDATED:
        await handleSubscriptionUpdated(db, event.data.object as Stripe.Subscription);
        break;

      case STRIPE_WEBHOOK_EVENTS.CUSTOMER_SUBSCRIPTION_DELETED:
        await handleSubscriptionDeleted(db, event.data.object as Stripe.Subscription);
        break;

      case STRIPE_WEBHOOK_EVENTS.INVOICE_PAYMENT_SUCCEEDED:
        await handleInvoicePaymentSucceeded(db, event.data.object as Stripe.Invoice);
        break;

      case STRIPE_WEBHOOK_EVENTS.INVOICE_PAYMENT_FAILED:
        await handleInvoicePaymentFailed(db, event.data.object as Stripe.Invoice);
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return Response.json({ received: true });
  } catch (error) {
    console.error("Stripe webhook error:", error);
    return Response.json({ error: "Webhook handler failed" }, { status: 400 });
  }
}

async function handleSubscriptionCreated(db: DB, subscription: Stripe.Subscription) {
  try {
    const priceId = subscription.items.data[0]?.price.id;
    if (!priceId) {
      console.error("No price ID found in subscription");
      return;
    }

    const [packageData] = await packageQueries.getPackageByStripePriceId(db, priceId);
    if (!packageData) {
      console.error(`Package not found for price ID: ${priceId}`);
      return;
    }

    // Find existing subscription or create new one
    const existingSubscription = await subscriptionQueries.getSubscriptionByStripeId(db, subscription.id);

    if (existingSubscription) {
      // Update existing subscription
      await subscriptionQueries.updateSubscriptionByStripeId(db, subscription.id, {
        status: subscription.status as any,
        currentPeriodStart: new Date(subscription.items.data[0].current_period_start * 1000),
        currentPeriodEnd: new Date(subscription.items.data[0].current_period_end * 1000),
        cancelAtPeriodEnd: subscription.cancel_at_period_end,
        trialStart: subscription.trial_start ? new Date(subscription.trial_start * 1000) : null,
        trialEnd: subscription.trial_end ? new Date(subscription.trial_end * 1000) : null,
      });
    } else {
      const stripeCustomer = await createOrGetStripeCustomer(db, subscription.metadata.userId);

      await subscriptionQueries.insertSubscription(db, {
        userId: subscription.metadata.userId,
        packageId: packageData.id,
        stripeSubscriptionId: subscription.id,
        stripeCustomerId: stripeCustomer.id,
        status: subscription.status as any,
        currentPeriodStart: new Date(subscription.items.data[0].current_period_start * 1000),
        currentPeriodEnd: new Date(subscription.items.data[0].current_period_end * 1000),
        cancelAtPeriodEnd: subscription.cancel_at_period_end,
        trialStart: subscription.trial_start ? new Date(subscription.trial_start * 1000) : null,
        trialEnd: subscription.trial_end ? new Date(subscription.trial_end * 1000) : null,
      });
    }
  } catch (error) {
    console.error("Error handling subscription created:", error);
  }
}

async function handleSubscriptionUpdated(db: DB, subscription: Stripe.Subscription) {
  try {
    await subscriptionQueries.updateSubscriptionByStripeId(db, subscription.id, {
      status: subscription.status as any,
      currentPeriodStart: new Date(subscription.items.data[0].current_period_start * 1000),
      currentPeriodEnd: new Date(subscription.items.data[0].current_period_end * 1000),
      cancelAtPeriodEnd: subscription.cancel_at_period_end,
      canceledAt: subscription.canceled_at ? new Date(subscription.canceled_at * 1000) : null,
      trialStart: subscription.trial_start ? new Date(subscription.trial_start * 1000) : null,
      trialEnd: subscription.trial_end ? new Date(subscription.trial_end * 1000) : null,
    });
  } catch (error) {
    console.error("Error handling subscription updated:", error);
  }
}

async function handleSubscriptionDeleted(db: DB, subscription: Stripe.Subscription) {
  try {
    await subscriptionQueries.updateSubscriptionByStripeId(db, subscription.id, {
      status: "canceled",
      canceledAt: new Date(),
    });
  } catch (error) {
    console.error("Error handling subscription deleted:", error);
  }
}

async function handleInvoicePaymentSucceeded(db: DB, invoice: Stripe.Invoice) {
  try {
    const subscriptionId = invoice.parent?.subscription_details?.subscription;
    if (subscriptionId) {
      const subscription = await subscriptionQueries.getSubscriptionByStripeId(db, subscriptionId as string);
      if (subscription) {
        // Update subscription status to active on successful payment
        await subscriptionQueries.updateSubscription(db, subscription.id, {
          status: "active",
        });
      }
    }
  } catch (error) {
    console.error("Error handling invoice payment succeeded:", error);
  }
}

async function handleInvoicePaymentFailed(db: DB, invoice: Stripe.Invoice) {
  try {
    const subscriptionId = invoice.parent?.subscription_details?.subscription;
    if (subscriptionId) {
      const subscription = await subscriptionQueries.getSubscriptionByStripeId(db, subscriptionId as string);
      if (subscription) {
        // Update subscription status to past_due on failed payment
        await subscriptionQueries.updateSubscription(db, subscription.id, {
          status: "past_due",
        });
      }
    }
  } catch (error) {
    console.error("Error handling invoice payment failed:", error);
  }
}
