"use server";

import * as packageQueries from "@/database/drizzle/queries/packages";
import { getDataContext } from "@/lib/getDataContext";
import { getUserSubscriptionStatus } from "@/lib/subscription-service";

export type Data = Awaited<ReturnType<typeof getData>>;

export async function getData() {
  const { db, session } = await getDataContext();
  // Get all available packages
  const packages = await packageQueries.getAllPackages(db);

  // Get user subscription status if user is logged in
  let subscriptionStatus = null;
  if (session?.user) {
    subscriptionStatus = await getUserSubscriptionStatus(db, session.user.id);
  }

  return {
    packages,
    subscriptionStatus,
    user: session?.user || null,
  };
}
