"use client";

import { CheckCircle, XCircle } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useData } from "./data.client";
import { PackageCard } from "./PackageCard";

export function Packages() {
  const data = useData();
  const { t } = useTranslation();
  const searchParams = useSearchParams();
  const [showAlert, setShowAlert] = useState(false);
  const [alertType, setAlertType] = useState<"success" | "error" | null>(null);
  const router = useRouter();

  useEffect(() => {
    if (searchParams.get("success") === "true") {
      setAlertType("success");
      setShowAlert(true);
      // Clear the URL parameters
      window.history.replaceState({}, "", "/packages");
      router.push("/");
    } else if (searchParams.get("canceled") === "true") {
      setAlertType("error");
      setShowAlert(true);
      // Clear the URL parameters
      window.history.replaceState({}, "", "/packages");
    }
  }, [router, searchParams]);

  return (
    <div className="container mx-auto px-4 py-8">
      {showAlert && (
        <div className="mb-8">
          {alertType === "success" ? (
            <div className="border border-green-200 bg-green-50 rounded-lg p-4 flex items-center gap-3">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <span className="text-green-800">{t("Payment successful! Your subscription is now active.")}</span>
            </div>
          ) : (
            <div className="border border-red-200 bg-red-50 rounded-lg p-4 flex items-center gap-3">
              <XCircle className="h-4 w-4 text-red-600" />
              <span className="text-red-800">{t("Payment was canceled. You can try again anytime.")}</span>
            </div>
          )}
        </div>
      )}

      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">{t("Choose Your Plan")}</h1>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          {t("Select the perfect plan for your video creation needs. Upgrade or downgrade at any time.")}
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
        {data.packages?.map((pkg) => (
          <PackageCard
            key={pkg.id}
            package={pkg}
            currentSubscription={data.subscriptionStatus?.subscription}
            user={data.user!}
          />
        ))}
      </div>

      {data.subscriptionStatus?.hasActiveSubscription && (
        <div className="mt-12 text-center">
          <div className="bg-green-50 border border-green-200 rounded-lg p-6 max-w-md mx-auto">
            <h3 className="text-lg font-semibold text-green-800 mb-2">{t("Current Plan")}</h3>
            <p className="text-green-700">
              {t("You have an active subscription to")} {data.subscriptionStatus.subscription?.package?.name}
            </p>
            {data.subscriptionStatus.currentPeriodEnd && (
              <p className="text-sm text-green-600 mt-2">
                {t("Renews on")} {new Date(data.subscriptionStatus.currentPeriodEnd).toLocaleDateString()}
              </p>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
