import { drizzle } from "drizzle-orm/node-postgres";
import * as auth from "./schema/auth";
import * as files from "./schema/files";
import * as getlate from "./schema/getlate";
import * as jobs from "./schema/jobs";
import * as packages from "./schema/packages";
import * as prompts from "./schema/prompts";
import * as translations from "./schema/translations";
import * as videos from "./schema/videos";

export function dbInit() {
  return drizzle(process.env.DATABASE_URL!, {
    schema: {
      ...auth,
      ...files,
      ...getlate,
      ...videos,
      ...jobs,
      ...packages,
      ...prompts,
      ...translations,
    },
  });
}
