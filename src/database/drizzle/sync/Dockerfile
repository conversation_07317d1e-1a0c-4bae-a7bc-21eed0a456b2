FROM node:lts-alpine AS builder

WORKDIR /app/

RUN npm init -y
RUN npm i drizzle-orm drizzle-kit pg tsx
RUN npm pkg set scripts.seed="tsx src/database/drizzle/seeds/index.ts"

FROM node:lts-alpine AS runner

WORKDIR /app

COPY --from=builder /app/node_modules ./node_modules
COPY src/database/drizzle/ src/database/drizzle/
COPY package.json .

CMD ["sh", "./src/database/drizzle/sync/script.sh"]

EXPOSE 5003