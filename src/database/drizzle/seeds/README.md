# Database Seeds

This directory contains database seeding scripts to populate your database with default data.

## Available Seeds

### Packages (`packages.ts`)

Creates default subscription packages with the following tiers:

- **Free**: 5 videos/month, 720p quality, 1GB storage
- **Starter**: 25 videos/month, 1080p quality, 5GB storage ($9.99/month)
- **Pro**: 100 videos/month, 4K quality, 20GB storage ($29.99/month)
- **Enterprise**: Unlimited videos, 4K quality, unlimited storage ($99.99/month)
- **Pro Annual**: Same as Pro but billed annually with 20% discount ($287.88/year)
- **Enterprise Annual**: Same as Enterprise but billed annually with 20% discount ($959.88/year)

## Usage

### Run All Seeds

```bash
npm run seed
```

### Run Specific Seeds

```bash
# Run only package seeds
npm run seed:packages
```

### Manual Execution

You can also run seeds directly with tsx:

```bash
npx tsx seeds/packages.ts
npx tsx seeds/index.ts
```

## Environment Setup

Make sure you have:

1. A `.env` file with `DATABASE_URL` configured
2. Database migrations applied (`npm run drizzle:migrate`)
3. Database connection available

### Database Connection Options

**Option 1: Using Docker (Recommended)**

```bash
# Start the database container
docker-compose up -d db

# Wait a moment for the database to be ready, then run seeds
npm run seed
```

**Option 2: Using Local PostgreSQL**

1. Install and start PostgreSQL locally
2. Create a database named `shorts`
3. Update your `.env` file:
   ```
   DATABASE_URL="postgresql://admin:password@localhost:5432/shorts"
   ```
4. Run migrations and seeds:
   ```bash
   npm run drizzle:migrate
   npm run seed
   ```

## Adding New Seeds

1. Create a new seed file in this directory (e.g., `users.ts`)
2. Follow the pattern of existing seed files:
   - Import necessary dependencies
   - Define default data
   - Create an async seed function
   - Export the function and data
   - Add conditional execution for direct runs
3. Import and call your seed function in `index.ts`
4. Optionally add a specific script in `package.json`

## Notes

- Seeds are designed to be idempotent when possible
- Check for existing data before inserting to avoid duplicates
- Use proper error handling and logging
- Seeds should work with both development and production databases
