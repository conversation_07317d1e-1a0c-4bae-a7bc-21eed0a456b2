import "dotenv/config";
import { seedPackages } from "./packages";

async function runSeeds() {
  console.log("🌱 Starting database seeding...");

  try {
    // Run all seed functions
    await seedPackages();

    console.log("✅ All seeds completed successfully!");
    process.exit(0);
  } catch (error) {
    console.error("❌ Seeding failed:", error);
    process.exit(1);
  }
}

// Run seeds if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runSeeds();
}

export { runSeeds };
