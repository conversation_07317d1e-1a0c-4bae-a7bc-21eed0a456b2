import { relations } from "drizzle-orm";
import { pgTable, text, timestamp, uuid } from "drizzle-orm/pg-core";
import { user } from "./auth";

export const userRelations = relations(user, ({ one }) => ({
  getLateProfile: one(getlateProfileTable),
}));

export const getlateProfileTable = pgTable("getlate_profiles", {
  id: uuid().primaryKey().defaultRandom(),
  userId: text().references(() => user.id, { onDelete: "cascade" }),
  getlateProfileId: text().notNull(),
  createdAt: timestamp().defaultNow(),
});

export type GetlateProfileItem = typeof getlateProfileTable.$inferSelect;
export type GetlateProfileInsert = typeof getlateProfileTable.$inferInsert;

export const getlateProfileRelations = relations(getlateProfileTable, ({ one, many }) => ({
  user: one(user, {
    fields: [getlateProfileTable.userId],
    references: [user.id],
  }),
  accounts: many(getlateAccountTable),
}));

export const getlateAccountTable = pgTable("getlate_accounts", {
  id: uuid().primaryKey().defaultRandom(),
  profileId: uuid()
    .notNull()
    .references(() => getlateProfileTable.id, { onDelete: "cascade" }),
  getlateAccountId: text().notNull(),
  platform: text().notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at")
    .defaultNow()
    .$onUpdate(() => /* @__PURE__ */ new Date())
    .notNull(),
});

export type GetlateAccountItem = typeof getlateAccountTable.$inferSelect;
export type GetlateAccountInsert = typeof getlateAccountTable.$inferInsert;

export const getlateAccountRelations = relations(getlateAccountTable, ({ one }) => ({
  profile: one(getlateProfileTable, {
    fields: [getlateAccountTable.profileId],
    references: [getlateProfileTable.id],
  }),
}));
