import { pgTable, text, timestamp } from "drizzle-orm/pg-core";

export const translationTable = pgTable("translations", {
  id: text().primaryKey(),
  en: text(),
  fr: text(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at")
    .defaultNow()
    .$onUpdate(() => /* @__PURE__ */ new Date())
    .notNull(),
});

// You can then infer the types for selecting and inserting
export type TranslationItem = typeof translationTable.$inferSelect;
export type TranslationInsert = typeof translationTable.$inferInsert;
