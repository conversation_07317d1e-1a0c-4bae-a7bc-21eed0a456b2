import { relations } from "drizzle-orm";
import { pgTable, text, timestamp, uuid } from "drizzle-orm/pg-core";
import { user } from "./auth";

export const fileTable = pgTable("files", {
  id: uuid().primaryKey().defaultRandom(),
  url: text().notNull(),
  mime: text(),
  type: text({ enum: ["image", "video"] }),
  userId: text().references(() => user.id, { onDelete: "cascade" }),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at")
    .defaultNow()
    .$onUpdate(() => /* @__PURE__ */ new Date())
    .notNull(),
});

export type FileItem = typeof fileTable.$inferSelect;
export type FileInsert = typeof fileTable.$inferInsert;

export const userRelations = relations(user, ({ many }) => ({
  files: many(fileTable),
}));

export const fileRelations = relations(fileTable, ({ one }) => ({
  user: one(user, {
    fields: [fileTable.userId],
    references: [user.id],
  }),
}));
