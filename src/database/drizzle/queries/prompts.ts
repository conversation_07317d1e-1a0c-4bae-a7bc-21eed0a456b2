import { prompts } from "@/lib/prompts";
import { eq } from "drizzle-orm";
import { PromptInsert, PromptItem, promptTable } from "../schema/prompts";

export function getAllPrompts(db: DB) {
  return db.select().from(promptTable);
}

export function getPrompt(db: DB, id: keyof typeof prompts) {
  return db.select().from(promptTable).where(eq(promptTable.id, id)).limit(1);
}

export async function getOrCreatePrompt(db: DB, id: keyof typeof prompts): Promise<PromptItem> {
  // First try to get existing prompt from database
  const existing = await getPrompt(db, id);

  if (existing.length > 0) {
    return existing[0];
  }

  // If not found, create with default values from prompts.ts
  const defaultPrompt = prompts[id];
  const newPrompt: PromptInsert = {
    id,
    model: defaultPrompt.model,
    prompt: defaultPrompt.prompt,
    maxTokens: defaultPrompt.maxTokens,
  };

  const created = await db.insert(promptTable).values(newPrompt).returning();
  return created[0];
}

export function updatePrompt(db: DB, id: keyof typeof prompts, data: Partial<PromptItem>) {
  return db
    .update(promptTable)
    .set({
      ...data,
      updatedAt: new Date(),
    })
    .where(eq(promptTable.id, id))
    .returning();
}

export function insertPrompt(db: DB, data: PromptInsert) {
  return db.insert(promptTable).values(data).returning();
}

export function deletePrompt(db: DB, id: keyof typeof prompts) {
  return db.delete(promptTable).where(eq(promptTable.id, id)).returning();
}
