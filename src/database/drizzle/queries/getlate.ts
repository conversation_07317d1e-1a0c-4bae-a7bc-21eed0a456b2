import { eq } from "drizzle-orm";
import {
  GetlateAccountInsert,
  getlateAccountTable,
  GetlateProfileInsert,
  getlateProfileTable,
} from "../schema/getlate";

export function getGetlateProfile(db: DB, userId: string) {
  return db.query.getlateProfileTable.findFirst({
    where: eq(getlateProfileTable.userId, userId),
    with: { accounts: true },
  });
}

export function insertGetlateProfile(db: DB, data: GetlateProfileInsert) {
  return db.insert(getlateProfileTable).values(data);
}

export function insertGetlateAccount(db: DB, data: GetlateAccountInsert) {
  return db.insert(getlateAccountTable).values(data);
}

export function deleteGetlateAccount(db: DB, getlateAccountId: string) {
  return db.delete(getlateAccountTable).where(eq(getlateAccountTable.getlateAccountId, getlateAccountId));
}
