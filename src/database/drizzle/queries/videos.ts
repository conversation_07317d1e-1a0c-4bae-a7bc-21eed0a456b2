import { eq } from "drizzle-orm";
import { jobTable } from "../schema/jobs";
import { VideoIdeaInsert, videoIdeaTable, VideoInsert, VideoItem, videoTable } from "../schema/videos";

export function insertVideo(db: DB, data: VideoInsert) {
  return db.insert(videoTable).values(data);
}

export function updateVideo(db: DB, id: string, data: Partial<VideoItem>) {
  return db.update(videoTable).set(data).where(eq(videoTable.id, id));
}

export function getAllVideos(db: DB, userId: string) {
  return db.select().from(videoTable).where(eq(videoTable.userId, userId));
}

export function getVideo(db: DB, id: string) {
  return db.select().from(videoTable).where(eq(videoTable.id, id));
}

export function getPendingVideos(db: DB, userId: string) {
  return db.query.videoTable.findMany({
    where: eq(videoTable.userId, userId),
    with: {
      job: {
        where: eq(jobTable.status, "pending"),
      } as any,
    },
  });
}

export function insertVideoIdea(db: DB, data: VideoIdeaInsert) {
  return db.insert(videoIdeaTable).values(data);
}
