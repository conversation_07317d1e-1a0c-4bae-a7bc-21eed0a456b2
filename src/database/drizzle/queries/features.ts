import { FeatureType } from "@/lib/features";
import { and, eq } from "drizzle-orm";
import { FeatureInsert, FeatureItem, featureTable } from "../schema/packages";

export function getAllFeatures(db: DB) {
  return db.select().from(featureTable);
}

export function getFeaturesByPackageId(db: DB, packageId: string) {
  return db.select().from(featureTable).where(eq(featureTable.packageId, packageId));
}

export function getFeature(db: DB, type: FeatureType, packageId: string) {
  return db
    .select()
    .from(featureTable)
    .where(and(eq(featureTable.type, type), eq(featureTable.packageId, packageId)))
    .limit(1);
}

export function insertFeature(db: DB, data: FeatureInsert) {
  return db.insert(featureTable).values(data).returning();
}

export function updateFeature(db: DB, type: FeatureType, packageId: string, data: Partial<FeatureItem>) {
  return db
    .update(featureTable)
    .set({
      ...data,
      updatedAt: new Date(),
    })
    .where(and(eq(featureTable.type, type), eq(featureTable.packageId, packageId)))
    .returning();
}

export function deleteFeature(db: DB, type: FeatureType, packageId: string) {
  return db
    .delete(featureTable)
    .where(and(eq(featureTable.type, type), eq(featureTable.packageId, packageId)))
    .returning();
}

export function deleteFeaturesByPackageId(db: DB, packageId: string) {
  return db.delete(featureTable).where(eq(featureTable.packageId, packageId)).returning();
}

/**
 * Create or edit a feature for a package
 * If the feature already exists, it will be updated; otherwise, it will be created
 */
export async function createOrEditFeature(
  db: DB,
  type: FeatureType,
  packageId: string,
  data: Omit<FeatureInsert, "type" | "packageId">,
): Promise<FeatureItem> {
  const existingFeature = await getFeature(db, type, packageId);

  if (existingFeature.length > 0) {
    // Update existing feature
    const [updatedFeature] = await updateFeature(db, type, packageId, data);
    return updatedFeature;
  } else {
    // Create new feature
    const [newFeature] = await insertFeature(db, {
      type,
      packageId,
      ...data,
    });
    return newFeature;
  }
}
