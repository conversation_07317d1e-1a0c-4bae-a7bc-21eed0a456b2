import { desc, eq } from "drizzle-orm";
import { PackageInsert, PackageItem, packageTable } from "../schema/packages";

export function getAllPackages(db: DB) {
  return db.select().from(packageTable).where(eq(packageTable.isActive, true)).orderBy(desc(packageTable.priority));
}

export function getAllPackagesForAdmin(db: DB) {
  return db.select().from(packageTable).orderBy(desc(packageTable.priority));
}

export function getPackageById(db: DB, id: string) {
  return db.select().from(packageTable).where(eq(packageTable.id, id)).limit(1);
}

export function getPackageByStripeProductId(db: DB, stripeProductId: string) {
  return db.select().from(packageTable).where(eq(packageTable.stripeProductId, stripeProductId)).limit(1);
}

export function getPackageByStripePriceId(db: DB, stripePriceId: string) {
  return db.select().from(packageTable).where(eq(packageTable.stripePriceId, stripePriceId)).limit(1);
}

export function insertPackage(db: DB, data: PackageInsert) {
  return db.insert(packageTable).values(data).returning();
}

export function updatePackage(db: DB, id: string, data: Partial<PackageItem>) {
  return db
    .update(packageTable)
    .set({
      ...data,
      updatedAt: new Date(),
    })
    .where(eq(packageTable.id, id))
    .returning();
}

export function deactivatePackage(db: DB, id: string) {
  return db
    .update(packageTable)
    .set({
      isActive: false,
      updatedAt: new Date(),
    })
    .where(eq(packageTable.id, id))
    .returning();
}

export function togglePackageStatus(db: DB, id: string, isActive: boolean) {
  return db
    .update(packageTable)
    .set({
      isActive,
      updatedAt: new Date(),
    })
    .where(eq(packageTable.id, id))
    .returning();
}
