import fileTranslations from "@/assets/translations.json";
import { redisClient } from "@/lib/redis";
import { eq } from "drizzle-orm";
import { cloneDeep } from "es-toolkit";
import { TranslationInsert, TranslationItem, translationTable } from "../schema/translations";

// Cache key for translations
const TRANSLATIONS_CACHE_KEY = "translations:all";
const CACHE_TTL = 3600; // 1 hour in seconds

// Type for the merged translations structure
type TranslationsData = {
  [language: string]: {
    translation: {
      [key: string]: string;
    };
  };
};

/**
 * Get all translations from database
 */
export async function getTranslationsFromDb(db: DB): Promise<TranslationItem[]> {
  return db.select().from(translationTable);
}

/**
 * Merge file translations with database translations
 * Database translations override file translations for the same keys
 */
function mergeTranslations(fileTranslations: TranslationsData, dbTranslations: TranslationItem[]): TranslationsData {
  const merged = cloneDeep(fileTranslations);

  // Process each database translation
  for (const dbTranslation of dbTranslations) {
    const { id, en, fr } = dbTranslation;

    // Add English translation if provided
    if (en) {
      if (!merged.en) {
        merged.en = { translation: {} };
      }
      merged.en.translation[id] = en;
    }

    // Add French translation if provided
    if (fr) {
      if (!merged.fr) {
        merged.fr = { translation: {} };
      }
      merged.fr.translation[id] = fr;
    }
  }

  return merged;
}

/**
 * Get all translations merged from file and database with Redis caching
 */
export async function getAllTranslations(db: DB): Promise<TranslationsData> {
  try {
    // Try to get from cache first
    const cached = await redisClient.get(TRANSLATIONS_CACHE_KEY);
    if (cached) {
      return JSON.parse(cached);
    }

    // Load from file and database
    const dbTranslations = await getTranslationsFromDb(db);

    // Merge translations
    const mergedTranslations = mergeTranslations(fileTranslations, dbTranslations);

    // Cache the result
    await redisClient.setex(TRANSLATIONS_CACHE_KEY, CACHE_TTL, JSON.stringify(mergedTranslations));

    return mergedTranslations;
  } catch (error) {
    console.error("Error getting all translations:", error);
    // Fallback to file translations only
    return fileTranslations;
  }
}

/**
 * Update a translation in the database and invalidate cache
 */
export async function updateTranslation(db: DB, data: TranslationInsert): Promise<TranslationItem> {
  try {
    // Update or insert the translation
    const result = await db
      .insert(translationTable)
      .values(data)
      .onConflictDoUpdate({
        target: translationTable.id,
        set: {
          en: data.en,
          fr: data.fr,
          updatedAt: new Date(),
        },
      })
      .returning();

    // Invalidate cache
    await redisClient.del(TRANSLATIONS_CACHE_KEY);

    return result[0];
  } catch (error) {
    console.error("Error updating translation:", error);
    throw error;
  }
}

/**
 * Get a specific translation by ID
 */
export async function getTranslation(db: DB, id: string): Promise<TranslationItem | undefined> {
  try {
    const result = await db.select().from(translationTable).where(eq(translationTable.id, id)).limit(1);
    return result[0];
  } catch (error) {
    console.error("Error getting translation:", error);
    return undefined;
  }
}

/**
 * Delete a translation and invalidate cache
 */
export async function deleteTranslation(db: DB, id: string): Promise<void> {
  try {
    await db.delete(translationTable).where(eq(translationTable.id, id));

    // Invalidate cache
    await redisClient.del(TRANSLATIONS_CACHE_KEY);
  } catch (error) {
    console.error("Error deleting translation:", error);
    throw error;
  }
}

/**
 * Manually invalidate the translations cache
 */
export async function invalidateTranslationsCache(): Promise<void> {
  try {
    await redisClient.del(TRANSLATIONS_CACHE_KEY);
  } catch (error) {
    console.error("Error invalidating translations cache:", error);
  }
}
