import { eq } from "drizzle-orm";
import { JobInsert, JobItem, jobTable } from "../schema/jobs";

export function insertJob(db: DB, data: JobInsert) {
  return db.insert(jobTable).values(data);
}

export function updateJob(db: DB, id: string, data: Partial<JobItem>) {
  return db.update(jobTable).set(data).where(eq(jobTable.id, id));
}

export function getAllJobs(db: DB) {
  return db.select().from(jobTable);
}

export function getJob(db: DB, id: string) {
  return db.select().from(jobTable).where(eq(jobTable.id, id));
}
