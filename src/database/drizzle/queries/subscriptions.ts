import { and, count, desc, eq } from "drizzle-orm";
import { SubscriptionInsert, SubscriptionItem, subscriptionTable } from "../schema/packages";

export function getUserSubscription(db: DB, userId: string) {
  return db.query.subscriptionTable.findFirst({
    where: eq(subscriptionTable.userId, userId),
    with: {
      package: true,
    },
    orderBy: desc(subscriptionTable.createdAt),
  });
}

export function getActiveUserSubscription(db: DB, userId: string) {
  return db.query.subscriptionTable.findFirst({
    where: and(eq(subscriptionTable.userId, userId), eq(subscriptionTable.status, "active")),
    with: {
      package: true,
    },
    orderBy: desc(subscriptionTable.createdAt),
  });
}

export function getSubscriptionByStripeId(db: DB, stripeSubscriptionId: string) {
  return db.query.subscriptionTable.findFirst({
    where: eq(subscriptionTable.stripeSubscriptionId, stripeSubscriptionId),
    with: {
      package: true,
      user: true,
    },
  });
}

export function getSubscriptionByStripeCustomerId(db: DB, stripeCustomerId: string) {
  return db.query.subscriptionTable.findFirst({
    where: eq(subscriptionTable.stripeCustomerId, stripeCustomerId),
    with: {
      package: true,
      user: true,
    },
    orderBy: desc(subscriptionTable.createdAt),
  });
}

export function getAllUserSubscriptions(db: DB, userId: string) {
  return db.query.subscriptionTable.findMany({
    where: eq(subscriptionTable.userId, userId),
    with: {
      package: true,
    },
    orderBy: desc(subscriptionTable.createdAt),
  });
}

export function insertSubscription(db: DB, data: SubscriptionInsert) {
  return db.insert(subscriptionTable).values(data).returning();
}

export function updateSubscription(db: DB, id: string, data: Partial<SubscriptionItem>) {
  return db
    .update(subscriptionTable)
    .set({
      ...data,
      updatedAt: new Date(),
    })
    .where(eq(subscriptionTable.id, id))
    .returning();
}

export function updateSubscriptionByStripeId(db: DB, stripeSubscriptionId: string, data: Partial<SubscriptionItem>) {
  return db
    .update(subscriptionTable)
    .set({
      ...data,
      updatedAt: new Date(),
    })
    .where(eq(subscriptionTable.stripeSubscriptionId, stripeSubscriptionId))
    .returning();
}

export function cancelSubscription(db: DB, id: string) {
  return db
    .update(subscriptionTable)
    .set({
      status: "canceled",
      canceledAt: new Date(),
      updatedAt: new Date(),
    })
    .where(eq(subscriptionTable.id, id))
    .returning();
}

export function getTotalSubscriptionsCount(db: DB) {
  return db.select({ count: count() }).from(subscriptionTable);
}

export function getActiveSubscriptionsCount(db: DB) {
  return db.select({ count: count() }).from(subscriptionTable).where(eq(subscriptionTable.status, "active"));
}
