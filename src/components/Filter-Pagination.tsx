"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Option, Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ChevronLeftIcon, ChevronRightIcon } from "lucide-react";
import Link from "next/link";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useCallback, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { SearchInput } from "./SearchInput";

export function useFilterAndPaginationParams() {
  const searchParams = useSearchParams();

  return useMemo(() => {
    const currentPage = parseInt(searchParams.get("page") || "1");
    const searchValue = searchParams.get("search") || "";
    const searchField = searchParams.get("searchField") || "email";
    const sortBy = searchParams.get("sortBy") || "createdAt";
    const sortDirection = searchParams.get("sortDirection") || "desc";
    const limit = parseInt(searchParams.get("limit") || "10");

    const offset = (currentPage - 1) * limit;

    return {
      currentPage,
      searchValue,
      searchField,
      sortBy,
      sortDirection,
      limit,
      offset,
    };
  }, [searchParams]);
}

export function useUpdateSearchParams() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  return useCallback(
    (updates: Record<string, string | number>) => {
      const params = new URLSearchParams(searchParams.toString());

      Object.entries(updates).forEach(([key, value]) => {
        if (value) {
          params.set(key, value.toString());
        } else {
          params.delete(key);
        }
      });

      // Reset to page 1 when search changes
      if ("search" in updates || "searchField" in updates) {
        params.set("page", "1");
      }

      router.push(`${pathname}?${params.toString()}`);
    },
    [pathname, router, searchParams],
  );
}

export function SearchAndFilter({ searchFields, sortFields }: { searchFields: Option[]; sortFields: Option[] }) {
  const { t } = useTranslation();
  const pathname = usePathname();
  const { searchValue, searchField, sortBy, sortDirection } = useFilterAndPaginationParams();
  const updateSearchParams = useUpdateSearchParams();

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex">
          {t("Search and Filter")}
          <Button variant="outline" size="sm" asChild className="ms-auto">
            <Link href={pathname}>{t("Reset")}</Link>
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="space-y-2">
            <Label htmlFor="search">{t("Search")}</Label>
            <SearchInput id="search" value={searchValue} setValue={(value) => updateSearchParams({ search: value })} />
          </div>

          <div className="space-y-2">
            <Label>{t("Search Field")}</Label>
            <Select value={searchField} onValueChange={(value) => updateSearchParams({ searchField: value })}>
              <SelectTrigger className="w-full">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {searchFields.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>{t("Sort By")}</Label>
            <Select value={sortBy} onValueChange={(value) => updateSearchParams({ sortBy: value })}>
              <SelectTrigger className="w-full">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {sortFields.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>{t("Sort Direction")}</Label>
            <Select value={sortDirection} onValueChange={(value) => updateSearchParams({ sortDirection: value })}>
              <SelectTrigger className="w-full">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="asc">{t("Ascending")}</SelectItem>
                <SelectItem value="desc">{t("Descending")}</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function Pagination({ total }: { total: number }) {
  const { t } = useTranslation();
  const { currentPage, limit, offset } = useFilterAndPaginationParams();
  const totalPages = Math.ceil(total / limit);

  const updateSearchParams = useUpdateSearchParams();

  return (
    totalPages > 0 && (
      <div className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          {t("Showing {{start}} to {{end}} of {{count}} items", {
            start: offset + 1,
            end: Math.min(offset + limit, total),
            count: total,
          })}
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => updateSearchParams({ page: currentPage - 1 })}
            disabled={currentPage <= 1}
          >
            <ChevronLeftIcon className="h-4 w-4" />
            {t("Previous")}
          </Button>

          <div className="flex items-center gap-1">
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              let pageNum;
              if (totalPages <= 5) {
                pageNum = i + 1;
              } else if (currentPage <= 3) {
                pageNum = i + 1;
              } else if (currentPage >= totalPages - 2) {
                pageNum = totalPages - 4 + i;
              } else {
                pageNum = currentPage - 2 + i;
              }

              return (
                <Button
                  key={pageNum}
                  variant={currentPage === pageNum ? "default" : "outline"}
                  size="sm"
                  onClick={() => updateSearchParams({ page: pageNum })}
                >
                  {pageNum}
                </Button>
              );
            })}
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={() => updateSearchParams({ page: currentPage + 1 })}
            disabled={currentPage >= totalPages}
          >
            {t("Next")}
            <ChevronRightIcon className="h-4 w-4" />
          </Button>
        </div>
      </div>
    )
  );
}
