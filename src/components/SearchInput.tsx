"use client";

import { cn } from "@/lib/utils";
import { debounce } from "es-toolkit";
import { SearchIcon, XIcon } from "lucide-react";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "./ui/button";
import { Input } from "./ui/input";

export function SearchInput({
  value: debouncedValue,
  setValue: _setValue,
  ...props
}: {
  value: string;
  setValue: (searchTerm: string) => void;
} & React.ComponentProps<typeof Input>) {
  const { t } = useTranslation();
  const [value, setValue] = useState(debouncedValue);
  const debouncedSetSearchTerm = debounce(_setValue, 500);

  return (
    <div className="relative">
      <span className="absolute left-0 top-0 h-full px-3 py-2 hover:bg-transparent">
        <SearchIcon className="text-muted-foreground" />
      </span>
      <Input
        placeholder={t("Search")}
        value={value}
        onChange={(e) => {
          setValue(e.target.value);
          debouncedSetSearchTerm(e.target.value);
        }}
        {...props}
        className={cn("pl-12", props.className)}
      />
      {value && (
        <Button
          type="button"
          variant="ghost"
          size="sm"
          className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
          onClick={() => {
            setValue("");
            _setValue("");
          }}
        >
          <XIcon className="text-muted-foreground" />
        </Button>
      )}
    </div>
  );
}
