"use server";

import { getAllFiles } from "@/database/drizzle/queries/files";
import { errors } from "@/lib/errors";
import { getDataContext } from "@/lib/getDataContext";
import { searchStockPhotos } from "@/lib/searchPhotos";

export async function onSearchStockPhotos(...args: Parameters<typeof searchStockPhotos>) {
  const context = await getDataContext();

  if (!context.session?.user) return { error: errors.NotAuthenticated };

  return await searchStockPhotos(...args);
}

export async function onGetMyUploads() {
  const context = await getDataContext();

  if (!context.session?.user) return { error: errors.NotAuthenticated };

  return await getAllFiles(context.db, {
    type: "image",
    userId: context.session.user.id,
  });
}
